@import url('https://fonts.googleapis.com/css2?family=Inter+Tight:wght@300;400;500;600;700;900&display=swap');
@import "tailwindcss";

:root {
  /* Dark Theme Colors */
  --background: #0a0a0a;
  --background-secondary: #1a1a1a;
  --background-tertiary: #2a2a2a;
  --foreground: #ffffff;
  --foreground-secondary: #f0f0f0;
  --foreground-tertiary: #e0e0e0;
  --foreground-muted: #a0a0a0;

  /* Blue/Purple Accent Colors */
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --primary-light: #60a5fa;
  --secondary: #8b5cf6;
  --secondary-dark: #7c3aed;
  --secondary-light: #a78bfa;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #1d4ed8 0%, #7c3aed 100%);
  --gradient-background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  --gradient-card: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);

  /* Shadows and Glows */
  --glow-primary: 0 0 20px rgba(59, 130, 246, 0.3);
  --glow-secondary: 0 0 20px rgba(139, 92, 246, 0.3);
  --shadow-card: 0 10px 25px rgba(0, 0, 0, 0.3);

  /* Typography */
  --font-sans: 'Inter Tight', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', Consolas, Monaco, "Courier New", monospace;

  /* Border Radius */
  --border-radius: 0.75rem;
  --border-radius-lg: 1rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
}

body {
  background: var(--gradient-background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Gradient Text Effects */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-text-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-secondary);
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  box-shadow: var(--glow-primary);
}

/* Smooth transitions */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--primary), 0 0 10px var(--primary), 0 0 15px var(--primary);
  }
  50% {
    box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary), 0 0 30px var(--primary);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-30px) translateX(20px);
  }
  66% {
    transform: translateY(10px) translateX(-15px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

/* Animation Classes */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.float {
  animation: float 6s ease-in-out infinite;
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.particle-float {
  animation: particleFloat 8s ease-in-out infinite;
}

/* Delay classes for staggered animations */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* Interactive Element Styles */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--glow-primary), var(--shadow-card);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.btn-secondary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--glow-secondary), var(--shadow-card);
}

.card {
  background: var(--gradient-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-card);
  backdrop-filter: blur(10px);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--glow-primary), var(--shadow-card);
  border-color: rgba(59, 130, 246, 0.3);
}

.input-field {
  background: var(--background-secondary);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  color: var(--foreground);
  padding: 0.75rem 1rem;
}

.input-field:focus {
  border-color: var(--primary);
  box-shadow: var(--glow-primary);
  background: var(--background-tertiary);
}

.nav-link {
  color: var(--foreground-secondary);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
}

.nav-link:hover {
  color: var(--foreground);
  background: rgba(59, 130, 246, 0.1);
  box-shadow: var(--glow-primary);
  transform: scale(1.05);
}

/* Floating Particle Background */
.particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 50%;
  opacity: 0.6;
}

.particle::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: var(--gradient-primary);
  border-radius: 50%;
  filter: blur(2px);
  opacity: 0.3;
}

.particle-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  opacity: 0.2;
  animation: particleFloat 10s ease-in-out infinite;
}

/* Decorative Elements */
.decorative-blob {
  position: absolute;
  border-radius: 50%;
  background: var(--gradient-primary);
  opacity: 0.1;
  filter: blur(40px);
  animation: float 8s ease-in-out infinite;
}

.decorative-blob-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decorative-blob-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  background: var(--gradient-secondary);
}

.decorative-blob-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .gradient-text {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }

  .card {
    margin: 0.5rem;
  }

  .btn-primary, .btn-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}


