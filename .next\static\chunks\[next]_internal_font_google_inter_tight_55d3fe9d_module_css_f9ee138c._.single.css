/* [next]/internal/font/google/inter_tight_55d3fe9d.module.css [app-client] (css) */
@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight Fallback;
  src: local(Arial);
  ascent-override: 100.51%;
  descent-override: 25.03%;
  line-gap-override: 0.0%;
  size-adjust: 96.39%;
}

.inter_tight_55d3fe9d-module__JpQgqG__className {
  font-family: Inter Tight, Inter Tight Fallback;
  font-style: normal;
}

.inter_tight_55d3fe9d-module__JpQgqG__variable {
  --font-inter-tight: "Inter Tight", "Inter Tight Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_inter_tight_55d3fe9d_module_css_f9ee138c._.single.css.map*/