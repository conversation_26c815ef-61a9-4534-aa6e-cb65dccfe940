@import "https://fonts.googleapis.com/css2?family=Inter+Tight:wght@300;400;500;600;700;900&display=swap";
/* [next]/internal/font/google/inter_tight_55d3fe9d.module.css [app-client] (css) */
@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsK8ahuQ2e8Smg-s.437e49d9.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsD8ahuQ2e8Smg-s.57b477cb.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsL8ahuQ2e8Smg-s.ac02836b.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsE8ahuQ2e8Smg-s.fdb2d775.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsI8ahuQ2e8Smg-s.2b41fb85.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsJ8ahuQ2e8Smg-s.d0af4f94.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter Tight;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/NGSwv5HMAFg6IuGlBNMjxLsH8ahuQ2e8-s.p.7cd063b6.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Tight Fallback;
  src: local(Arial);
  ascent-override: 100.51%;
  descent-override: 25.03%;
  line-gap-override: 0.0%;
  size-adjust: 96.39%;
}

.inter_tight_55d3fe9d-module__JpQgqG__className {
  font-family: Inter Tight, Inter Tight Fallback;
  font-style: normal;
}

.inter_tight_55d3fe9d-module__JpQgqG__variable {
  --font-inter-tight: "Inter Tight", "Inter Tight Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-emerald-300: oklch(84.5% .143 164.978);
    --color-emerald-500: oklch(69.6% .17 162.48);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-indigo-300: oklch(78.5% .115 274.713);
    --color-indigo-400: oklch(67.3% .182 276.935);
    --color-indigo-500: oklch(58.5% .233 277.117);
    --color-purple-300: oklch(82.7% .119 306.383);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-light: 300;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-relaxed: 1.625;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, .2, 1) infinite;
    --blur-sm: 8px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .z-10 {
    z-index: 10;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-6 {
    margin: calc(var(--spacing) * 6);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .inline {
    display: inline;
  }

  .inline-flex {
    display: inline-flex;
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .animate-ping {
    animation: var(--animate-ping);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .resize {
    resize: both;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-blue-500\/20 {
    border-color: #3080ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-blue-500\/20 {
      border-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .border-blue-500\/30 {
    border-color: #3080ff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-blue-500\/30 {
      border-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
    }
  }

  .border-emerald-500\/20 {
    border-color: #00bb7f33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-emerald-500\/20 {
      border-color: color-mix(in oklab, var(--color-emerald-500) 20%, transparent);
    }
  }

  .border-gray-500\/30 {
    border-color: #6a72824d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-500\/30 {
      border-color: color-mix(in oklab, var(--color-gray-500) 30%, transparent);
    }
  }

  .border-green-500\/20 {
    border-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-500\/20 {
      border-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .border-green-500\/30 {
    border-color: #00c7584d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-500\/30 {
      border-color: color-mix(in oklab, var(--color-green-500) 30%, transparent);
    }
  }

  .border-indigo-500\/20 {
    border-color: #625fff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-indigo-500\/20 {
      border-color: color-mix(in oklab, var(--color-indigo-500) 20%, transparent);
    }
  }

  .border-indigo-500\/30 {
    border-color: #625fff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-indigo-500\/30 {
      border-color: color-mix(in oklab, var(--color-indigo-500) 30%, transparent);
    }
  }

  .border-purple-500\/20 {
    border-color: #ac4bff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-purple-500\/20 {
      border-color: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .border-purple-500\/30 {
    border-color: #ac4bff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-purple-500\/30 {
      border-color: color-mix(in oklab, var(--color-purple-500) 30%, transparent);
    }
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-red-500\/30 {
    border-color: #fb2c364d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-500\/30 {
      border-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);
    }
  }

  .border-white\/5 {
    border-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/5 {
      border-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .border-white\/10 {
    border-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/10 {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-yellow-500\/30 {
    border-color: #edb2004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-yellow-500\/30 {
      border-color: color-mix(in oklab, var(--color-yellow-500) 30%, transparent);
    }
  }

  .border-t-blue-500 {
    border-top-color: var(--color-blue-500);
  }

  .bg-black\/30 {
    background-color: #0000004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/30 {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-500\/10 {
    background-color: #3080ff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/10 {
      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .bg-blue-500\/20 {
    background-color: #3080ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/20 {
      background-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .bg-emerald-500\/10 {
    background-color: #00bb7f1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-emerald-500\/10 {
      background-color: color-mix(in oklab, var(--color-emerald-500) 10%, transparent);
    }
  }

  .bg-gray-500\/20 {
    background-color: #6a728233;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-500\/20 {
      background-color: color-mix(in oklab, var(--color-gray-500) 20%, transparent);
    }
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-500\/20 {
    background-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-500\/20 {
      background-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .bg-indigo-500\/10 {
    background-color: #625fff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-indigo-500\/10 {
      background-color: color-mix(in oklab, var(--color-indigo-500) 10%, transparent);
    }
  }

  .bg-indigo-500\/20 {
    background-color: #625fff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-indigo-500\/20 {
      background-color: color-mix(in oklab, var(--color-indigo-500) 20%, transparent);
    }
  }

  .bg-purple-500\/10 {
    background-color: #ac4bff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/10 {
      background-color: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .bg-purple-500\/20 {
    background-color: #ac4bff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/20 {
      background-color: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-500\/10 {
    background-color: #fb2c361a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-500\/10 {
      background-color: color-mix(in oklab, var(--color-red-500) 10%, transparent);
    }
  }

  .bg-red-500\/20 {
    background-color: #fb2c3633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-500\/20 {
      background-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-yellow-500\/20 {
    background-color: #edb20033;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-yellow-500\/20 {
      background-color: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);
    }
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-blue-400\/20 {
    --tw-gradient-from: #54a2ff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-400\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-400) 20%, transparent);
    }
  }

  .from-blue-500\/10 {
    --tw-gradient-from: #3080ff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-500\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .from-blue-500\/20 {
    --tw-gradient-from: #3080ff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-500\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .from-blue-600\/90 {
    --tw-gradient-from: #155dfce6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-600\/90 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-600) 90%, transparent);
    }
  }

  .from-blue-900\/20 {
    --tw-gradient-from: #1c398e33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-900\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
    }
  }

  .from-red-500\/20 {
    --tw-gradient-from: #fb2c3633;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-red-500\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
  }

  .to-purple-400\/20 {
    --tw-gradient-to: #c07eff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-400\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-400) 20%, transparent);
    }
  }

  .to-purple-500\/10 {
    --tw-gradient-to: #ac4bff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-500\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .to-purple-500\/20 {
    --tw-gradient-to: #ac4bff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-500\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .to-purple-600\/90 {
    --tw-gradient-to: #9810fae6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-600\/90 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-600) 90%, transparent);
    }
  }

  .to-purple-900\/20 {
    --tw-gradient-to: #59168b33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-900\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
    }
  }

  .to-red-600\/20 {
    --tw-gradient-to: #e4001433;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-red-600\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-600) 20%, transparent);
    }
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .text-center {
    text-align: center;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .text-blue-200 {
    color: var(--color-blue-200);
  }

  .text-blue-300 {
    color: var(--color-blue-300);
  }

  .text-blue-400 {
    color: var(--color-blue-400);
  }

  .text-emerald-300 {
    color: var(--color-emerald-300);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-green-300 {
    color: var(--color-green-300);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-indigo-300 {
    color: var(--color-indigo-300);
  }

  .text-indigo-400 {
    color: var(--color-indigo-400);
  }

  .text-purple-300 {
    color: var(--color-purple-300);
  }

  .text-purple-400 {
    color: var(--color-purple-400);
  }

  .text-red-300 {
    color: var(--color-red-300);
  }

  .text-red-400 {
    color: var(--color-red-400);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/90 {
    color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/90 {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .italic {
    font-style: italic;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .placeholder-gray-500::placeholder {
    color: var(--color-gray-500);
  }

  .opacity-25 {
    opacity: .25;
  }

  .opacity-75 {
    opacity: .75;
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .delay-200 {
    transition-delay: .2s;
  }

  .delay-300 {
    transition-delay: .3s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-\[1\.02\]:hover {
      scale: 1.02;
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-500\/50:hover {
      border-color: #3080ff80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-blue-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-gray-500\/50:hover {
      border-color: #6a728280;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-gray-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-gray-500) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-green-500\/50:hover {
      border-color: #00c75880;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-green-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-green-500) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-indigo-500\/50:hover {
      border-color: #625fff80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-indigo-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-indigo-500) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-purple-500\/50:hover {
      border-color: #ac4bff80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-purple-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-purple-500) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-red-500\/50:hover {
      border-color: #fb2c3680;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-red-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-500\/30:hover {
      background-color: #3080ff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-blue-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-500\/30:hover {
      background-color: #6a72824d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-gray-500) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-500\/30:hover {
      background-color: #00c7584d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-green-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-green-500) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-indigo-500\/30:hover {
      background-color: #625fff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-indigo-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-indigo-500) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-purple-500\/30:hover {
      background-color: #ac4bff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-purple-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-purple-500) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-500\/30:hover {
      background-color: #fb2c364d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-red-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/10:hover {
      background-color: #ffffff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/10:hover {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-300:hover {
      color: var(--color-blue-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-300:hover {
      color: var(--color-gray-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-300:hover {
      color: var(--color-green-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-indigo-300:hover {
      color: var(--color-indigo-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-purple-300:hover {
      color: var(--color-purple-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-300:hover {
      color: var(--color-red-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-blue-500\/20:hover {
      --tw-shadow-color: #3080ff33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-blue-500\/20:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:shadow-gray-500\/20:hover {
      --tw-shadow-color: #6a728233;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-gray-500\/20:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-gray-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:shadow-green-500\/20:hover {
      --tw-shadow-color: #00c75833;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-green-500\/20:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-green-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:shadow-indigo-500\/20:hover {
      --tw-shadow-color: #625fff33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-indigo-500\/20:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-indigo-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:shadow-purple-500\/20:hover {
      --tw-shadow-color: #ac4bff33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-purple-500\/20:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-purple-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:shadow-red-500\/20:hover {
      --tw-shadow-color: #fb2c3633;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-red-500\/20:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  .focus\:placeholder-gray-400:focus::placeholder {
    color: var(--color-gray-400);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .disabled\:transform-none:disabled {
    transform: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 64rem) {
    .lg\:items-center {
      align-items: center;
    }
  }
}

:root {
  --background: #0a0a0a;
  --background-secondary: #1a1a1a;
  --background-tertiary: #2a2a2a;
  --foreground: #fff;
  --foreground-secondary: #f0f0f0;
  --foreground-tertiary: #e0e0e0;
  --foreground-muted: #a0a0a0;
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --primary-light: #60a5fa;
  --secondary: #8b5cf6;
  --secondary-dark: #7c3aed;
  --secondary-light: #a78bfa;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #1d4ed8 0%, #7c3aed 100%);
  --gradient-background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  --gradient-card: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  --glow-primary: 0 0 20px #3b82f64d;
  --glow-secondary: 0 0 20px #8b5cf64d;
  --shadow-card: 0 10px 25px #0000004d;
  --font-sans: "Inter Tight", ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: "JetBrains Mono", Consolas, Monaco, "Courier New", monospace;
  --border-radius: .75rem;
  --border-radius-lg: 1rem;
}

body {
  background: var(--gradient-background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  overflow-x: hidden;
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-text-fill-color: transparent;
  color: #0000;
  -webkit-background-clip: text;
  background-clip: text;
}

.gradient-text-secondary {
  background: var(--gradient-secondary);
  -webkit-text-fill-color: transparent;
  color: #0000;
  -webkit-background-clip: text;
  background-clip: text;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-secondary);
}

:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  box-shadow: var(--glow-primary);
}

* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter;
  transition-duration: .3s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0)rotate(0);
  }

  50% {
    transform: translateY(-20px)rotate(5deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--primary), 0 0 10px var(--primary), 0 0 15px var(--primary);
  }

  50% {
    box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary), 0 0 30px var(--primary);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0)translateX(0);
  }

  33% {
    transform: translateY(-30px)translateX(20px);
  }

  66% {
    transform: translateY(10px)translateX(-15px);
  }

  100% {
    transform: translateY(0)translateX(0);
  }
}

.fade-in-up {
  animation: .6s ease-out forwards fadeInUp;
}

.float {
  animation: 6s ease-in-out infinite float;
}

.pulse {
  animation: 2s ease-in-out infinite pulse;
}

.glow {
  animation: 2s ease-in-out infinite alternate glow;
}

.particle-float {
  animation: 8s ease-in-out infinite particleFloat;
}

.delay-100 {
  animation-delay: .1s;
}

.delay-200 {
  animation-delay: .2s;
}

.delay-300 {
  animation-delay: .3s;
}

.delay-400 {
  animation-delay: .4s;
}

.delay-500 {
  animation-delay: .5s;
}

.btn-primary {
  background: var(--gradient-primary);
  color: #fff;
  border-radius: var(--border-radius);
  cursor: pointer;
  box-shadow: var(--shadow-card);
  border: none;
  padding: .75rem 1.5rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  box-shadow: var(--glow-primary), var(--shadow-card);
  transform: translateY(-2px)scale(1.02);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: #fff;
  border-radius: var(--border-radius);
  cursor: pointer;
  box-shadow: var(--shadow-card);
  border: none;
  padding: .75rem 1.5rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.btn-secondary:hover {
  box-shadow: var(--glow-secondary), var(--shadow-card);
  transform: translateY(-2px)scale(1.02);
}

.card {
  background: var(--gradient-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-card);
  backdrop-filter: blur(10px);
  border: 1px solid #ffffff1a;
}

.card:hover {
  box-shadow: var(--glow-primary), var(--shadow-card);
  border-color: #3b82f64d;
  transform: translateY(-5px);
}

.input-field {
  background: var(--background-secondary);
  border-radius: var(--border-radius);
  color: var(--foreground);
  border: 1px solid #ffffff1a;
  padding: .75rem 1rem;
}

.input-field:focus {
  border-color: var(--primary);
  box-shadow: var(--glow-primary);
  background: var(--background-tertiary);
}

.nav-link {
  color: var(--foreground-secondary);
  border-radius: var(--border-radius);
  padding: .5rem 1rem;
  font-weight: 500;
  text-decoration: none;
}

.nav-link:hover {
  color: var(--foreground);
  box-shadow: var(--glow-primary);
  background: #3b82f61a;
  transform: scale(1.05);
}

.particle-background {
  pointer-events: none;
  z-index: -1;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.particle {
  background: var(--gradient-primary);
  opacity: .6;
  border-radius: 50%;
  width: 4px;
  height: 4px;
  position: absolute;
}

.particle:before {
  content: "";
  background: var(--gradient-primary);
  filter: blur(2px);
  opacity: .3;
  border-radius: 50%;
  position: absolute;
  inset: -1px;
}

.particle-line {
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  opacity: .2;
  height: 1px;
  animation: 10s ease-in-out infinite particleFloat;
  position: absolute;
}

.decorative-blob {
  background: var(--gradient-primary);
  opacity: .1;
  filter: blur(40px);
  border-radius: 50%;
  animation: 8s ease-in-out infinite float;
  position: absolute;
}

.decorative-blob-1 {
  width: 300px;
  height: 300px;
  animation-delay: 0s;
  top: 10%;
  left: 10%;
}

.decorative-blob-2 {
  background: var(--gradient-secondary);
  width: 200px;
  height: 200px;
  animation-delay: 2s;
  top: 60%;
  right: 15%;
}

.decorative-blob-3 {
  width: 150px;
  height: 150px;
  animation-delay: 4s;
  bottom: 20%;
  left: 20%;
}

@media (width <= 768px) {
  .gradient-text {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }

  .card {
    margin: .5rem;
  }

  .btn-primary, .btn-secondary {
    padding: .625rem 1.25rem;
    font-size: .875rem;
  }
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    opacity: 0;
    transform: scale(2);
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__6f402e6d._.css.map*/