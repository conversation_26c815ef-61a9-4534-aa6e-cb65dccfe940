"""
Firebase Authentication middleware for Flask backend
Provides secure token verification and user authentication
"""

import os
import json
import logging
from functools import wraps
from typing import Optional, Dict, Any

import firebase_admin
from firebase_admin import credentials, auth
from flask import request, jsonify, g

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FirebaseAuth:
    """Firebase Authentication handler for Flask backend"""
    
    def __init__(self):
        self.app = None
        self._initialized = False
        
    def init_app(self, app):
        """Initialize Firebase Admin SDK with Flask app"""
        self.app = app
        
        try:
            # Check if Firebase is already initialized
            if not firebase_admin._apps:
                self._initialize_firebase()
            
            self._initialized = True
            logger.info("Firebase Admin SDK initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase Admin SDK: {e}")
            self._initialized = False
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK with credentials"""
        
        # Try to get credentials from environment variables
        firebase_config = self._get_firebase_config_from_env()
        
        if firebase_config:
            # Initialize with service account credentials from environment
            cred = credentials.Certificate(firebase_config)
            firebase_admin.initialize_app(cred)
            logger.info("Firebase initialized with environment credentials")
        else:
            # Try to initialize with service account key file
            service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT_KEY_PATH')
            if service_account_path and os.path.exists(service_account_path):
                cred = credentials.Certificate(service_account_path)
                firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized with service account key file")
            else:
                # Initialize with default credentials (for development)
                firebase_admin.initialize_app()
                logger.info("Firebase initialized with default credentials")
    
    def _get_firebase_config_from_env(self) -> Optional[Dict[str, str]]:
        """Get Firebase configuration from environment variables"""
        
        required_fields = [
            'FIREBASE_PROJECT_ID',
            'FIREBASE_PRIVATE_KEY_ID', 
            'FIREBASE_PRIVATE_KEY',
            'FIREBASE_CLIENT_EMAIL',
            'FIREBASE_CLIENT_ID',
            'FIREBASE_AUTH_URI',
            'FIREBASE_TOKEN_URI'
        ]
        
        config = {}
        for field in required_fields:
            value = os.getenv(field)
            if not value:
                logger.warning(f"Missing environment variable: {field}")
                return None
            config[field.lower().replace('firebase_', '')] = value
        
        # Handle private key formatting
        private_key = config.get('private_key', '')
        if private_key:
            # Replace escaped newlines with actual newlines
            config['private_key'] = private_key.replace('\\n', '\n')
        
        return config
    
    def verify_token(self, id_token: str) -> Optional[Dict[str, Any]]:
        """Verify Firebase ID token and return decoded claims"""
        
        if not self._initialized:
            logger.error("Firebase not initialized")
            return None
        
        try:
            # Verify the ID token
            decoded_token = auth.verify_id_token(id_token)
            logger.info(f"Token verified for user: {decoded_token.get('uid')}")
            return decoded_token
            
        except auth.InvalidIdTokenError:
            logger.warning("Invalid ID token")
            return None
        except auth.ExpiredIdTokenError:
            logger.warning("Expired ID token")
            return None
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return None
    
    def get_user_info(self, uid: str) -> Optional[Dict[str, Any]]:
        """Get user information from Firebase Auth"""
        
        if not self._initialized:
            return None
        
        try:
            user_record = auth.get_user(uid)
            return {
                'uid': user_record.uid,
                'email': user_record.email,
                'email_verified': user_record.email_verified,
                'display_name': user_record.display_name,
                'photo_url': user_record.photo_url,
                'disabled': user_record.disabled,
                'provider_data': [
                    {
                        'uid': provider.uid,
                        'email': provider.email,
                        'provider_id': provider.provider_id
                    }
                    for provider in user_record.provider_data
                ]
            }
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None

# Global Firebase Auth instance
firebase_auth = FirebaseAuth()

def require_auth(f):
    """Decorator to require Firebase authentication for Flask routes"""
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get the Authorization header
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return jsonify({
                'error': 'Missing Authorization header',
                'code': 'MISSING_AUTH_HEADER'
            }), 401
        
        # Extract the token from "Bearer <token>"
        try:
            scheme, token = auth_header.split(' ', 1)
            if scheme.lower() != 'bearer':
                raise ValueError("Invalid authorization scheme")
        except ValueError:
            return jsonify({
                'error': 'Invalid Authorization header format. Expected: Bearer <token>',
                'code': 'INVALID_AUTH_HEADER'
            }), 401
        
        # Verify the token
        decoded_token = firebase_auth.verify_token(token)
        
        if not decoded_token:
            return jsonify({
                'error': 'Invalid or expired token',
                'code': 'INVALID_TOKEN'
            }), 401
        
        # Store user info in Flask's g object for use in the route
        g.current_user = decoded_token
        g.user_id = decoded_token.get('uid')
        g.user_email = decoded_token.get('email')
        
        return f(*args, **kwargs)
    
    return decorated_function

def get_current_user() -> Optional[Dict[str, Any]]:
    """Get the current authenticated user from Flask's g object"""
    return getattr(g, 'current_user', None)

def get_current_user_id() -> Optional[str]:
    """Get the current authenticated user's ID"""
    return getattr(g, 'user_id', None)

def get_current_user_email() -> Optional[str]:
    """Get the current authenticated user's email"""
    return getattr(g, 'user_email', None)
