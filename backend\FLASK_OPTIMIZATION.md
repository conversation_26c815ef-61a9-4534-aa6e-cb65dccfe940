# Flask Development Server Optimization

## Problem

The Flask development server was experiencing severe performance issues due to excessive file watching and reloading. The server was monitoring all Python files in the system, including third-party packages in `site-packages`, causing:

- Constant server restarts
- Laggy animations on the frontend
- Poor development experience
- High CPU usage

## Root Cause

Flask's development server with `debug=True` uses the Werkzeug reloader, which by default uses the `watchdog` library to monitor file changes. On Windows, this was monitoring:

- All Python files in `site-packages` directory
- System-wide Python packages
- Hundreds of third-party library files

This caused the server to restart continuously as the file watcher detected changes in packages like `scipy`, `sqlalchemy`, `streamlit`, `tenacity`, etc.

## Solution

The solution implements several optimizations:

### 1. Reloader Type Change
- Changed from `watchdog` to `stat` reloader
- `stat` reloader is more conservative and doesn't monitor as many files
- Reduces false positive file change detections

### 2. Explicit File Monitoring
- Only monitor project files explicitly using `extra_files` parameter
- Exclude `site-packages` and system directories
- Monitor only relevant file types (`.py`, `.json`, `.txt`, `.md`, `.yml`, `.yaml`, `.env`)

### 3. Environment Configuration
- Added `.env` file for configuration
- Environment variables to control reloader behavior
- Configurable host, port, and reloader settings

### 4. Fallback Mechanism
- Graceful fallback if optimized configuration fails
- Option to disable reloader entirely if needed

## Usage

### Method 1: Direct Execution (Recommended)
```bash
cd backend
python app.py
```

### Method 2: Using the Optimized Starter Script
```bash
cd backend
python start_server.py
```

### Method 3: With Custom Options
```bash
cd backend
python start_server.py --host 127.0.0.1 --port 8000 --reloader-type stat
```

### Method 4: Disable Reloader (for debugging)
```bash
cd backend
python start_server.py --no-reloader
```

## Configuration

### Environment Variables (.env file)
```
FLASK_ENV=development
FLASK_DEBUG=1
FLASK_RELOADER_TYPE=stat
FLASK_RUN_EXTRA_FILES=
WERKZEUG_RUN_MAIN=true
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_LOG_LEVEL=INFO
```

### Key Configuration Options

- `FLASK_RELOADER_TYPE=stat`: Uses stat-based file monitoring instead of watchdog
- `FLASK_RUN_EXTRA_FILES=""`: Prevents automatic file discovery
- `WERKZEUG_RUN_MAIN=true`: Controls reloader behavior

## Benefits

1. **Eliminated Excessive Reloading**: Server no longer restarts constantly
2. **Improved Performance**: Reduced CPU usage and faster response times
3. **Better Development Experience**: Smooth animations and responsive UI
4. **Maintained Functionality**: Still reloads on actual project file changes
5. **Configurable**: Easy to adjust settings based on needs

## Monitoring

The optimized server will show:
```
Starting Flask development server with optimized file watching...
Monitoring X project files for changes
Using stat reloader to avoid site-packages monitoring
```

This confirms that only project files are being monitored, not system packages.

## Troubleshooting

If you still experience issues:

1. **Disable reloader completely**:
   ```bash
   python start_server.py --no-reloader
   ```

2. **Check for other file watchers**: Ensure no other tools are monitoring the same files

3. **Verify environment**: Make sure the `.env` file is being loaded correctly

4. **Use alternative reloader**:
   ```bash
   python start_server.py --reloader-type watchdog
   ```

## Technical Details

The optimization works by:

1. **Limiting File Scope**: Only monitoring files in the project directory
2. **Using Stat Reloader**: Polling-based monitoring instead of event-based
3. **Explicit File Lists**: Providing exact files to monitor via `extra_files`
4. **Environment Control**: Using environment variables to control Werkzeug behavior

This ensures that the development server remains responsive while still providing the convenience of automatic reloading for actual code changes.
