{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/services/api.ts"], "sourcesContent": ["'use client';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nexport interface ServerProperties {\n  [key: string]: string;\n}\n\nexport interface PlayerInfo {\n  online: number;\n  max: number;\n  list: string[];\n}\n\nexport interface Server {\n  id: string;\n  name: string;\n  port: number;\n  version: string;\n  memory: string;\n  status: string;\n  backup?: boolean;\n  container_id?: string;\n  properties?: ServerProperties;\n  players?: PlayerInfo;\n}\n\nexport interface ServerFormData {\n  name: string;\n  port: number;\n  version: string;\n  memory: string;\n}\n\nexport async function getServers(): Promise<Server[]> {\n  try {\n    console.log('Fetching servers from:', API_URL);\n    const response = await fetch(`${API_URL}/servers`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      cache: 'no-store',\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch servers: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching servers:', error);\n    throw error;\n  }\n}\n\nexport async function createServer(data: ServerFormData): Promise<Server> {\n  try {\n    const response = await fetch(`${API_URL}/servers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to create server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating server:', error);\n    throw error;\n  }\n}\n\nexport async function startServer(serverName: string): Promise<{ status: string; message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}/start`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to start server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error starting server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function stopServer(serverName: string): Promise<{ status: string; message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}/stop`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to stop server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error stopping server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function deleteServer(serverName: string): Promise<{ message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to delete server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error deleting server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function toggleBackup(serverName: string): Promise<{ message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}/backup`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to toggle backup: ${response.status} ${response.statusText}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(`Error toggling backup for server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function downloadBackup(serverName: string): Promise<void> {\n  try {\n    // Create a direct link to the backup endpoint\n    const backupUrl = `${API_URL}/servers/${serverName}/backup`;\n\n    // Open the URL in a new tab/window to trigger the download\n    window.open(backupUrl, '_blank');\n  } catch (error) {\n    console.error(`Error downloading backup for server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function getServerDetails(serverId: string): Promise<Server> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverId}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      cache: 'no-store',\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to fetch server details: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error fetching server details for ${serverId}:`, error);\n    throw error;\n  }\n}\n\nexport async function getServerLogs(serverId: string): Promise<{ logs: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverId}/logs`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      cache: 'no-store',\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to fetch server logs: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error fetching logs for server ${serverId}:`, error);\n    throw error;\n  }\n}\n\nexport async function sendServerCommand(serverId: string, command: string): Promise<{ response: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverId}/command`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      body: JSON.stringify({ command }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to send command: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error sending command to server ${serverId}:`, error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEgB;AAFhB;AAEA,MAAM,UAAU,iEAAmC;AAgC5C,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC,0BAA0B;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,CAAC,EAAE;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACtF;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,aAAa,IAAoB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,CAAC,EAAE;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACzG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,YAAY,UAAkB;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,WAAW,MAAM,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;QACtD,MAAM;IACR;AACF;AAEO,eAAe,WAAW,UAAkB;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,WAAW,KAAK,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACvG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;QACtD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,UAAkB;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,YAAY,EAAE;YAC/D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACzG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;QACtD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,UAAkB;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,WAAW,OAAO,CAAC,EAAE;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACzG;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,EAAE;QACjE,MAAM;IACR;AACF;AAEO,eAAe,eAAe,UAAkB;IACrD,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY,GAAG,QAAQ,SAAS,EAAE,WAAW,OAAO,CAAC;QAE3D,2DAA2D;QAC3D,OAAO,IAAI,CAAC,WAAW;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,WAAW,CAAC,CAAC,EAAE;QACpE,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,QAAgB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,gCAAgC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAChH;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;QAChE,MAAM;IACR;AACF;AAEO,eAAe,cAAc,QAAgB;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,SAAS,KAAK,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,6BAA6B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC7G;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,EAAE;QAC7D,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,QAAgB,EAAE,OAAe;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,SAAS,QAAQ,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC9D,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/ServerConsole.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { getServerLogs, sendServerCommand } from '../services/api';\n\ninterface ServerConsoleProps {\n  serverId: string;\n  isRunning: boolean;\n}\n\nexport default function ServerConsole({ serverId, isRunning }: ServerConsoleProps) {\n  const [logs, setLogs] = useState<string>('');\n  const [command, setCommand] = useState<string>('');\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const consoleRef = useRef<HTMLDivElement>(null);\n\n  // Fetch logs on component mount and when server status changes\n  useEffect(() => {\n    const fetchLogs = async () => {\n      try {\n        const response = await getServerLogs(serverId);\n        setLogs(response.logs || 'No logs available');\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to fetch logs');\n        console.error('Error fetching logs:', err);\n      }\n    };\n\n    fetchLogs();\n\n    // Set up polling for logs if server is running\n    let interval: NodeJS.Timeout | null = null;\n    if (isRunning) {\n      interval = setInterval(fetchLogs, 5000); // Poll every 5 seconds\n    }\n\n    return () => {\n      if (interval) {\n        clearInterval(interval);\n      }\n    };\n  }, [serverId, isRunning]);\n\n  // Auto-scroll to bottom when logs update\n  useEffect(() => {\n    if (consoleRef.current) {\n      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;\n    }\n  }, [logs]);\n\n  const handleSendCommand = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!command.trim() || !isRunning) return;\n\n    setIsLoading(true);\n\n    try {\n      const response = await sendServerCommand(serverId, command);\n\n      // Add the command and response to the logs\n      setLogs(prev => `${prev}\\n> ${command}\\n${response.response}`);\n\n      // Clear the command input\n      setCommand('');\n      setError(null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to send command');\n      console.error('Error sending command:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"card overflow-hidden\">\n      <div className=\"px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n        <h3 className=\"text-xl font-bold flex items-center gradient-text\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n          </svg>\n          Server Console\n        </h3>\n        <p className=\"text-gray-300 mt-2 font-light\">\n          {isRunning ? 'Interactive console for server management' : 'Server is not running. Start the server to send commands.'}\n        </p>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-500/10 border-l-4 border-red-500 p-6 m-6 rounded-lg backdrop-blur-sm\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-6 w-6 text-red-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h4 className=\"text-red-400 font-semibold\">Console Error</h4>\n              <p className=\"text-sm text-red-300 mt-1\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Console Output */}\n      <div\n        ref={consoleRef}\n        className=\"bg-black/50 text-green-400 font-mono text-sm p-6 h-96 overflow-y-auto border border-white/5 m-6 rounded-lg backdrop-blur-sm\"\n        style={{ fontFamily: 'JetBrains Mono, Consolas, Monaco, \"Courier New\", monospace' }}\n      >\n        <div className=\"flex items-center mb-4 pb-2 border-b border-green-500/20\">\n          <div className=\"flex space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n          </div>\n          <span className=\"ml-4 text-green-300 text-xs font-semibold\">Server Console</span>\n        </div>\n        <pre className=\"whitespace-pre-wrap break-words text-green-300 leading-relaxed\">\n          {logs || (\n            <span className=\"text-gray-500 italic\">\n              {isRunning ? 'Loading logs...' : 'Server is not running. No logs available.'}\n            </span>\n          )}\n        </pre>\n      </div>\n\n      {/* Command Input */}\n      <form onSubmit={handleSendCommand} className=\"flex items-center bg-black/30 border-t border-white/10 backdrop-blur-sm\">\n        <div className=\"text-green-400 px-6 py-4 select-none font-mono font-bold text-lg\">&gt;</div>\n        <input\n          type=\"text\"\n          value={command}\n          onChange={(e) => setCommand(e.target.value)}\n          placeholder={isRunning ? \"Type a command...\" : \"Server is not running\"}\n          disabled={!isRunning || isLoading}\n          className=\"flex-1 bg-transparent text-white px-2 py-4 focus:outline-none font-mono placeholder-gray-500 focus:placeholder-gray-400\"\n        />\n        <button\n          type=\"submit\"\n          disabled={!isRunning || isLoading || !command.trim()}\n          className=\"btn-primary mx-4 my-2 px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n        >\n          {isLoading ? (\n            <svg className=\"animate-spin h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n          ) : (\n            <span className=\"flex items-center\">\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n              </svg>\n              Send\n            </span>\n          )}\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAsB;;IAC/E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,+DAA+D;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;qDAAY;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE;wBACrC,QAAQ,SAAS,IAAI,IAAI;wBACzB,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAC9C,QAAQ,KAAK,CAAC,wBAAwB;oBACxC;gBACF;;YAEA;YAEA,+CAA+C;YAC/C,IAAI,WAAkC;YACtC,IAAI,WAAW;gBACb,WAAW,YAAY,WAAW,OAAO,uBAAuB;YAClE;YAEA;2CAAO;oBACL,IAAI,UAAU;wBACZ,cAAc;oBAChB;gBACF;;QACF;kCAAG;QAAC;QAAU;KAAU;IAExB,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC,SAAS,GAAG,WAAW,OAAO,CAAC,YAAY;YAChE;QACF;kCAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,WAAW;QAEnC,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;YAEnD,2CAA2C;YAC3C,QAAQ,CAAA,OAAQ,GAAG,KAAK,IAAI,EAAE,QAAQ,EAAE,EAAE,SAAS,QAAQ,EAAE;YAE7D,0BAA0B;YAC1B,WAAW;YACX,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAA6B,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACpH,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;kCAGR,6LAAC;wBAAE,WAAU;kCACV,YAAY,8CAA8C;;;;;;;;;;;;YAI9D,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAuB,OAAM;gCAA6B,SAAQ;gCAAY,MAAK;0CAChG,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAGlQ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAA6D;;kCAElF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,6LAAC;gCAAK,WAAU;0CAA4C;;;;;;;;;;;;kCAE9D,6LAAC;wBAAI,WAAU;kCACZ,sBACC,6LAAC;4BAAK,WAAU;sCACb,YAAY,oBAAoB;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAK,UAAU;gBAAmB,WAAU;;kCAC3C,6LAAC;wBAAI,WAAU;kCAAmE;;;;;;kCAClF,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wBAC1C,aAAa,YAAY,sBAAsB;wBAC/C,UAAU,CAAC,aAAa;wBACxB,WAAU;;;;;;kCAEZ,6LAAC;wBACC,MAAK;wBACL,UAAU,CAAC,aAAa,aAAa,CAAC,QAAQ,IAAI;wBAClD,WAAU;kCAET,0BACC,6LAAC;4BAAI,WAAU;4BAAuB,OAAM;4BAA6B,MAAK;4BAAO,SAAQ;;8CAC3F,6LAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,6LAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;iDAGrD,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GAxJwB;KAAA", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { user, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !user) {\n      router.push('/login');\n    }\n  }, [user, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center fade-in-up\">\n          <div className=\"relative mb-6\">\n            <div className=\"animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto\"></div>\n            <div className=\"absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto\"></div>\n          </div>\n          <h3 className=\"text-xl font-semibold text-white mb-2\">Loading</h3>\n          <p className=\"text-gray-300 font-light\">Please wait...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,MAAM;gBACvB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAA2B;;;;;;;;;;;;;;;;;IAIhD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,qBAAO;kBAAG;;AACZ;GA9BwB;;QACM,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/AdminHeader.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\n\nexport default function AdminHeader() {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = () => {\n    logout();\n    router.push('/login');\n  };\n\n  return (\n    <header className=\"relative overflow-hidden\">\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90 backdrop-blur-sm\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20\"></div>\n\n      <div className=\"relative z-10 container mx-auto flex justify-between items-center p-6\">\n        <div className=\"flex items-center fade-in-up\">\n          <div className=\"w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-br from-blue-400/20 to-purple-400/20 backdrop-blur-sm border border-white/10\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-7 w-7 text-blue-300\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm3 1h6v6H6V5z\" clipRule=\"evenodd\" />\n              <path d=\"M6 5H5v1h1V5z\" />\n              <path d=\"M5 8h1v1H5V8z\" />\n              <path d=\"M8 5h1v1H8V5z\" />\n              <path d=\"M8 8h1v1H8V8z\" />\n              <path d=\"M11 5h1v1h-1V5z\" />\n              <path d=\"M11 8h1v1h-1V8z\" />\n              <path d=\"M5 11h1v1H5v-1z\" />\n              <path d=\"M8 11h1v1H8v-1z\" />\n              <path d=\"M11 11h1v1h-1v-1z\" />\n            </svg>\n          </div>\n          <div>\n            <h1 className=\"text-2xl font-bold text-white\">Minecraft Server Admin Panel</h1>\n            <p className=\"text-blue-200 text-sm font-medium\">Welcome, <span className=\"gradient-text-secondary\">{user?.username}</span></p>\n          </div>\n        </div>\n\n        <nav className=\"flex items-center space-x-4 fade-in-up delay-200\">\n          <Link\n            href=\"/dashboard\"\n            className=\"nav-link text-white/90 hover:text-white font-medium px-4 py-2 rounded-xl hover:bg-white/10 transition-all duration-300\"\n          >\n            <svg className=\"h-5 w-5 mr-2 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n            </svg>\n            Dashboard\n          </Link>\n          <button\n            onClick={handleLogout}\n            className=\"btn-secondary px-6 py-2 font-semibold inline-flex items-center hover:scale-105 transition-all duration-300\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z\" clipRule=\"evenodd\" />\n            </svg>\n            Logout\n          </button>\n        </nav>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAwB,SAAQ;oCAAY,MAAK;;sDACjG,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAiF,UAAS;;;;;;sDACrH,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;0CAGZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAE,WAAU;;4CAAoC;0DAAS,6LAAC;gDAAK,WAAU;0DAA2B,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAI/G,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAsB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;0CAGR,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAe,SAAQ;wCAAY,MAAK;kDACxF,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA6K,UAAS;;;;;;;;;;;oCAC7M;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA3DwB;;QACG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/app/server/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport { getServerDetails, startServer, stopServer, deleteServer, toggleBackup, downloadBackup, Server } from '../../../services/api';\nimport ServerConsole from '../../../components/ServerConsole';\nimport ProtectedRoute from '../../../components/ProtectedRoute';\nimport AdminHeader from '../../../components/AdminHeader';\n\nexport default function ServerDetailsPage() {\n  const params = useParams();\n  const router = useRouter();\n  const serverId = params.id as string;\n\n  const [server, setServer] = useState<Server | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchServerDetails = async () => {\n      try {\n        const data = await getServerDetails(serverId);\n        setServer(data);\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An unknown error occurred');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (serverId) {\n      fetchServerDetails();\n    }\n  }, [serverId]);\n\n  const handleStartServer = async () => {\n    if (!server) return;\n\n    try {\n      await startServer(server.name);\n      setServer({ ...server, status: 'running' });\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleStopServer = async () => {\n    if (!server) return;\n\n    try {\n      await stopServer(server.name);\n      setServer({ ...server, status: 'stopped' });\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleDeleteServer = async () => {\n    if (!server) return;\n\n    if (!confirm(`Are you sure you want to delete the server \"${server.name}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await deleteServer(server.name);\n      // Navigate back to the dashboard\n      router.push('/dashboard');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleToggleBackup = async () => {\n    if (!server) return;\n\n    try {\n      await toggleBackup(server.name);\n      // Refresh server details\n      const updatedServer = await getServerDetails(serverId);\n      setServer(updatedServer);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleDownloadBackup = async () => {\n    if (!server) return;\n\n    try {\n      await downloadBackup(server.name);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'running':\n        return 'text-green-400 bg-green-500/20 border-green-500/30';\n      case 'stopped':\n        return 'text-red-400 bg-red-500/20 border-red-500/30';\n      case 'starting':\n        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';\n      default:\n        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <ProtectedRoute>\n        <div className=\"min-h-screen text-white\">\n          <AdminHeader />\n          <div className=\"flex items-center justify-center py-20\">\n            <div className=\"text-center fade-in-up\">\n              <div className=\"relative mb-6\">\n                <div className=\"animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto\"></div>\n                <div className=\"absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto\"></div>\n              </div>\n              <h3 className=\"text-xl font-semibold text-white mb-2\">Loading Server Details</h3>\n              <p className=\"text-gray-300 font-light\">Please wait while we fetch the server information...</p>\n            </div>\n          </div>\n        </div>\n      </ProtectedRoute>\n    );\n  }\n\n  if (error || !server) {\n    return (\n      <ProtectedRoute>\n        <div className=\"min-h-screen text-white\">\n          <AdminHeader />\n          <div className=\"container mx-auto py-12 px-4\">\n            <div className=\"card p-8 text-center fade-in-up\">\n              <div className=\"w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/20 flex items-center justify-center\">\n                <svg className=\"h-10 w-10 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold text-white mb-4\">Server Not Found</h3>\n              <p className=\"text-red-400 mb-8\">{error || 'The requested server could not be found.'}</p>\n              <button\n                onClick={() => router.push('/dashboard')}\n                className=\"btn-primary px-8 py-3 font-semibold\"\n              >\n                <svg className=\"h-5 w-5 mr-2 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n                </svg>\n                Back to Dashboard\n              </button>\n            </div>\n          </div>\n        </div>\n      </ProtectedRoute>\n    );\n  }\n\n  return (\n    <ProtectedRoute>\n      <div className=\"min-h-screen text-white\">\n        <AdminHeader />\n\n        <main className=\"container mx-auto py-12 px-4 max-w-7xl\">\n          <div className=\"mb-8 fade-in-up\">\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"text-blue-400 hover:text-blue-300 mb-4 inline-flex items-center font-medium transition-colors\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\n              </svg>\n              Back to Dashboard\n            </button>\n          </div>\n\n          {/* Server Info Card */}\n          <div className=\"card mb-8 fade-in-up delay-200\">\n            <div className=\"px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n              <div className=\"flex flex-col lg:flex-row lg:items-center justify-between gap-6\">\n                <div>\n                  <h1 className=\"text-4xl font-bold flex flex-col lg:flex-row lg:items-center gap-4 gradient-text\">\n                    {server.name}\n                    <span className={`px-4 py-2 rounded-full text-sm font-semibold border backdrop-blur-sm ${getStatusColor(server.status)} w-fit`}>\n                      {server.status.charAt(0).toUpperCase() + server.status.slice(1)}\n                    </span>\n                  </h1>\n                  <p className=\"text-gray-300 mt-2 font-light\">Server ID: <span className=\"font-mono text-blue-300\">{server.id}</span></p>\n                </div>\n                <div className=\"flex flex-wrap gap-3\">\n                  {server.status === 'stopped' ? (\n                    <button\n                      onClick={handleStartServer}\n                      className=\"bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/20\"\n                    >\n                      <svg className=\"h-5 w-5 mr-2 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Start Server\n                    </button>\n                  ) : (\n                    <button\n                      onClick={handleStopServer}\n                      className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/20\"\n                    >\n                      <svg className=\"h-5 w-5 mr-2 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Stop Server\n                    </button>\n                  )}\n                  <button\n                    onClick={handleDeleteServer}\n                    className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/20\"\n                  >\n                    <svg className=\"h-5 w-5 mr-2 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16\" />\n                    </svg>\n                    Delete Server\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"p-8\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                <div className=\"bg-blue-500/10 rounded-lg p-4 border border-blue-500/20\">\n                  <h3 className=\"text-blue-300 font-semibold mb-2\">Port</h3>\n                  <p className=\"text-2xl font-bold text-white\">{server.port}</p>\n                </div>\n                <div className=\"bg-purple-500/10 rounded-lg p-4 border border-purple-500/20\">\n                  <h3 className=\"text-purple-300 font-semibold mb-2\">Version</h3>\n                  <p className=\"text-2xl font-bold text-white\">{server.version}</p>\n                </div>\n                <div className=\"bg-indigo-500/10 rounded-lg p-4 border border-indigo-500/20\">\n                  <h3 className=\"text-indigo-300 font-semibold mb-2\">Memory</h3>\n                  <p className=\"text-2xl font-bold text-white\">{server.memory}</p>\n                </div>\n                <div className=\"bg-emerald-500/10 rounded-lg p-4 border border-emerald-500/20\">\n                  <h3 className=\"text-emerald-300 font-semibold mb-2\">Backup</h3>\n                  <div className=\"flex flex-col space-y-3\">\n                    <p className=\"text-2xl font-bold text-white\">{server.backup ? 'Enabled' : 'Disabled'}</p>\n                    <div className=\"flex flex-wrap gap-2\">\n                      <button\n                        onClick={handleToggleBackup}\n                        className={`px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105 ${\n                          server.backup\n                            ? 'bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-500/20'\n                            : 'bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/30 hover:border-gray-500/50 text-gray-400 hover:text-gray-300 hover:shadow-lg hover:shadow-gray-500/20'\n                        }`}\n                      >\n                        Toggle\n                      </button>\n                      {server.backup && (\n                        <button\n                          onClick={handleDownloadBackup}\n                          className=\"bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 hover:border-purple-500/50 text-purple-400 hover:text-purple-300 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20\"\n                        >\n                          Download\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Server Console */}\n          <div className=\"fade-in-up delay-300\">\n            <ServerConsole serverId={serverId} isRunning={server.status === 'running'} />\n          </div>\n        </main>\n\n        <footer className=\"relative mt-20\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm\"></div>\n          <div className=\"relative z-10 container mx-auto text-center py-8 px-4\">\n            <div className=\"border-t border-white/10 pt-8\">\n              <p className=\"text-sm text-gray-300 font-medium\">\n                Minecraft Server Admin Panel &copy; {new Date().getFullYear()}\n              </p>\n              <p className=\"text-xs mt-2 text-gray-400 font-light\">\n                Manage your Minecraft servers with ease\n              </p>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAE1B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB;oBACzB,IAAI;wBACF,MAAM,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;wBACpC,UAAU;wBACV,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,IAAI,UAAU;gBACZ;YACF;QACF;sCAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAI;YAC7B,UAAU;gBAAE,GAAG,MAAM;gBAAE,QAAQ;YAAU;QAC3C,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI;YAC5B,UAAU;gBAAE,GAAG,MAAM;gBAAE,QAAQ;YAAU;QAC3C,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,QAAQ,CAAC,4CAA4C,EAAE,OAAO,IAAI,CAAC,gCAAgC,CAAC,GAAG;YAC1G;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,IAAI;YAC9B,iCAAiC;YACjC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,IAAI;YAC9B,yBAAyB;YACzB,MAAM,gBAAgB,MAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;YAC7C,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,IAAI;QAClC,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC,uIAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,UAAW;;;;;kCACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpD;IAEA,IAAI,SAAS,CAAC,QAAQ;QACpB,qBACE,6LAAC,uIAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,UAAW;;;;;kCACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAe,SAAQ;kDAClE,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoH,UAAS;;;;;;;;;;;;;;;;8CAG5J,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAE,WAAU;8CAAqB,SAAS;;;;;;8CAC3C,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAsB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC7E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpB;IAEA,qBACE,6LAAC,uIAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oIAAA,CAAA,UAAW;;;;;8BAEZ,6LAAC;oBAAK,WAAU;;sCACd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,6LAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAe,SAAQ;wCAAY,MAAK;kDACxF,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwI,UAAS;;;;;;;;;;;oCACxK;;;;;;;;;;;;sCAMV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;4DACX,OAAO,IAAI;0EACZ,6LAAC;gEAAK,WAAW,CAAC,qEAAqE,EAAE,eAAe,OAAO,MAAM,EAAE,MAAM,CAAC;0EAC3H,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;kEAGjE,6LAAC;wDAAE,WAAU;;4DAAgC;0EAAW,6LAAC;gEAAK,WAAU;0EAA2B,OAAO,EAAE;;;;;;;;;;;;;;;;;;0DAE9G,6LAAC;gDAAI,WAAU;;oDACZ,OAAO,MAAM,KAAK,0BACjB,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAe,SAAQ;0EAC/D,cAAA,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAA0G,UAAS;;;;;;;;;;;4DAC1I;;;;;;6EAIR,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAe,SAAQ;0EAC/D,cAAA,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAmG,UAAS;;;;;;;;;;;4DACnI;;;;;;;kEAIV,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC7E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DACjE;;;;;;;;;;;;;;;;;;;;;;;;8CAOd,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAiC,OAAO,IAAI;;;;;;;;;;;;0DAE3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,6LAAC;wDAAE,WAAU;kEAAiC,OAAO,OAAO;;;;;;;;;;;;0DAE9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,6LAAC;wDAAE,WAAU;kEAAiC,OAAO,MAAM;;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAiC,OAAO,MAAM,GAAG,YAAY;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS;wEACT,WAAW,CAAC,uFAAuF,EACjG,OAAO,MAAM,GACT,sKACA,qKACJ;kFACH;;;;;;oEAGA,OAAO,MAAM,kBACZ,6LAAC;wEACC,SAAS;wEACT,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,sIAAA,CAAA,UAAa;gCAAC,UAAU;gCAAU,WAAW,OAAO,MAAM,KAAK;;;;;;;;;;;;;;;;;8BAIpE,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAoC;4CACV,IAAI,OAAO,WAAW;;;;;;;kDAE7D,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GA3RwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}