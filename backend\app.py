from flask import Flask, request, jsonify, send_file, g
from flask_cors import CORS
import os
import json
import sys
import uuid
import shutil
from datetime import datetime

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # python-dotenv is optional, continue without it
    pass

# Import Firebase authentication
from firebase_auth import firebase_auth, require_auth, get_current_user, get_current_user_id

app = Flask(__name__)

# Configure CORS with proper headers for Firebase Auth
CORS(app, resources={
    r"/api/*": {
        "origins": "*",
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"]
    }
})

# Initialize Firebase authentication
firebase_auth.init_app(app)

# Configure Flask development server to only watch project files
# This prevents the watchdog from monitoring site-packages and causing excessive reloads
if app.debug:
    # Set environment variables to control watchdog behavior
    os.environ['FLASK_RUN_EXTRA_FILES'] = ''

    # Configure watchdog to exclude site-packages and other system directories
    import site
    site_packages_dirs = site.getsitepackages()
    if hasattr(site, 'getusersitepackages'):
        site_packages_dirs.append(site.getusersitepackages())

    # Set up exclusion patterns for common directories that shouldn't trigger reloads
    excluded_patterns = []
    for site_dir in site_packages_dirs:
        if site_dir:
            excluded_patterns.append(site_dir)

    # Add other common directories to exclude
    excluded_patterns.extend([
        os.path.join(os.path.expanduser('~'), 'AppData'),
        os.path.join(os.path.expanduser('~'), '.cache'),
        os.path.join(os.path.expanduser('~'), '.local'),
        'node_modules',
        '.git',
        '__pycache__',
        '.pytest_cache',
        '.venv',
        'venv',
        'env'
    ])

    # Store excluded patterns for potential use by custom reloader
    app.config['EXCLUDED_WATCH_PATTERNS'] = excluded_patterns

# Initialize Docker client
docker_available = True
try:
    import docker
    docker_client = docker.from_env()
except Exception as e:
    docker_available = False
    print(f"Warning: Docker is not available. Server management operations will be simulated. Error: {e}", file=sys.stderr)

# Path to store server configurations
SERVERS_CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'servers.json')

# Initialize servers config file if it doesn't exist
if not os.path.exists(SERVERS_CONFIG_PATH):
    with open(SERVERS_CONFIG_PATH, 'w') as f:
        json.dump([], f)

def get_servers():
    """Read server configurations from file"""
    try:
        with open(SERVERS_CONFIG_PATH, 'r') as f:
            return json.load(f)
    except:
        return []

def save_servers(servers):
    """Save server configurations to file"""
    with open(SERVERS_CONFIG_PATH, 'w') as f:
        json.dump(servers, f)

def create_server_id():
    """Create a unique server ID"""
    return str(uuid.uuid4())

# Health check endpoint - no authentication required
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'firebase_initialized': firebase_auth._initialized
    })

# User profile endpoint
@app.route('/api/profile', methods=['GET'])
@require_auth
def get_profile():
    """Get current user profile"""
    user = get_current_user()
    user_id = get_current_user_id()

    # Get additional user info from Firebase
    user_info = firebase_auth.get_user_info(user_id) if user_id else None

    return jsonify({
        'user': user,
        'profile': user_info,
        'permissions': {
            'admin': True,  # For now, all authenticated users are admins
            'can_manage_servers': True,
            'can_upload_files': True
        }
    })

@app.route('/api/servers', methods=['GET'])
@require_auth
def list_servers():
    """List all Minecraft servers - requires authentication"""
    user_id = get_current_user_id()
    print(f"User {user_id} requested servers list", file=sys.stderr)
    return jsonify(get_servers())

@app.route('/api/servers/<server_id>', methods=['GET'])
@require_auth
def get_server_details(server_id):
    """Get detailed information about a specific Minecraft server"""
    servers = get_servers()

    # Find the server by ID
    server = next((s for s in servers if s['id'] == server_id), None)

    # If not found by ID, try finding by name (for backward compatibility)
    if not server:
        server = next((s for s in servers if s['name'] == server_id), None)

    if not server:
        return jsonify({'error': f'Server with ID/name {server_id} not found'}), 404

    # Get additional server details if available
    server_details = {**server}

    # Add server properties if available
    server_folder = os.path.join(os.path.dirname(__file__), 'servers', server['id'])
    properties_file = os.path.join(server_folder, 'server.properties')

    # Initialize empty properties dictionary
    server_details['properties'] = {}

    if os.path.exists(properties_file):
        try:
            with open(properties_file, 'r') as f:
                properties = {}
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key_value = line.split('=', 1)
                        if len(key_value) == 2:
                            properties[key_value[0]] = key_value[1]
                server_details['properties'] = properties
        except Exception as e:
            print(f"Error reading server properties: {e}", file=sys.stderr)
    else:
        # If server.properties doesn't exist, create some default properties for display
        server_details['properties'] = {
            'server-port': str(server['port']),
            'gamemode': 'survival',
            'difficulty': 'normal',
            'max-players': '20',
            'view-distance': '10',
            'enable-command-block': 'false',
            'motd': f"A Minecraft Server - {server['name']}",
            'pvp': 'true',
            'spawn-protection': '16',
            'level-name': 'world'
        }

    # Always add player information, with simulated data if needed
    # Default player info structure
    player_info = {
        'online': 0,
        'max': 20,
        'list': []
    }

    # If server is running, try to get real player data
    if server['status'] == 'running' and 'container_id' in server:
        try:
            if docker_available:
                # Get player list from the server
                container = docker_client.containers.get(server['container_id'])
                exec_result = container.exec_run('rcon-cli list')
                player_list_output = exec_result.output.decode('utf-8').strip()

                # Parse player information
                # Example output: "There are 2/20 players online: player1, player2"
                if "players online" in player_list_output:
                    try:
                        # Extract player count
                        count_match = player_list_output.split("There are ")[1].split(" players online")[0]
                        online, max_players = count_match.split('/')
                        player_info['online'] = int(online)
                        player_info['max'] = int(max_players)

                        # Extract player names if any
                        if ":" in player_list_output and player_info['online'] > 0:
                            players_part = player_list_output.split(": ")[1]
                            player_info['list'] = [p.strip() for p in players_part.split(", ")]
                    except Exception as e:
                        print(f"Error parsing player list: {e}", file=sys.stderr)
        except Exception as e:
            print(f"Error getting player information: {e}", file=sys.stderr)

    # Always add simulated player data for testing
    # This ensures the player insights section always has data to display
    if not docker_available or server['status'] != 'running':
        # Simulate different player counts based on server name for testing
        if 'Test' in server['name']:
            player_info = {
                'online': 2,
                'max': 20,
                'list': ['TestPlayer1', 'TestPlayer2']
            }
        elif 'Minecraft' in server['name']:
            player_info = {
                'online': 3,
                'max': 20,
                'list': ['Steve', 'Alex', 'Notch']
            }

    # Add player info to server details
    server_details['players'] = player_info

    return jsonify(server_details)

@app.route('/api/servers', methods=['POST'])
@require_auth
def create_server():
    """Create a new Minecraft server"""
    data = request.json

    # Validate required fields
    required_fields = ['name', 'port', 'version', 'memory']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400

    # Check if port is already in use
    servers = get_servers()
    for server in servers:
        if server['port'] == data['port']:
            return jsonify({'error': f'Port {data["port"]} is already in use'}), 400

    # Create server configuration with a unique ID
    server_id = create_server_id()
    server_config = {
        'id': server_id,
        'name': data['name'],
        'port': data['port'],
        'version': data['version'],
        'memory': data['memory'],
        'status': 'stopped',  # Initial status
        'backup': False
    }

    # Create server directory structure
    server_folder = os.path.join(os.path.dirname(__file__), 'servers', server_id)
    os.makedirs(server_folder, exist_ok=True)

    # Add server to configuration
    servers.append(server_config)
    save_servers(servers)

    return jsonify(server_config), 201

@app.route('/api/servers/<server_name>/start', methods=['POST'])
@require_auth
def start_server(server_name):
    """Start a Minecraft server"""
    servers = get_servers()

    # Find the server
    server = next((s for s in servers if s['name'] == server_name), None)
    if not server:
        return jsonify({'error': f'Server {server_name} not found'}), 404

    try:
        if docker_available:
            # Create and start the container
            container_name = f"minecraft_server_{server['id']}"
            container = docker_client.containers.run(
                'itzg/minecraft-server:latest',
                detach=True,
                name=container_name,
                ports={25565: server['port']},
                environment={
                    'EULA': 'TRUE',
                    'VERSION': server['version'],
                    'MEMORY': server['memory']
                },
                volumes={
                    f"/servers/{server['id']}": {
                        'bind': '/data',
                        'mode': 'rw'
                    }
                }
            )

            # Update server status
            server['status'] = 'running'
            server['container_id'] = container.id
        else:
            # Simulate starting the server when Docker is not available
            server['status'] = 'running'
            server['container_id'] = f"simulated-container-{server['id']}"
            print(f"Simulating start of server: {server_name} (ID: {server['id']}) (Docker not available)", file=sys.stderr)

        save_servers(servers)

        return jsonify({'status': 'running', 'message': f'Server {server_name} started successfully'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/servers/<server_name>/stop', methods=['POST'])
@require_auth
def stop_server(server_name):
    """Stop a Minecraft server"""
    servers = get_servers()

    # Find the server
    server = next((s for s in servers if s['name'] == server_name), None)
    if not server:
        return jsonify({'error': f'Server {server_name} not found'}), 404

    if 'container_id' not in server:
        return jsonify({'error': f'Server {server_name} is not running'}), 400

    try:
        if docker_available:
            # Stop the container
            container = docker_client.containers.get(server['container_id'])
            container.stop()
            container.remove()
        else:
            # Simulate stopping the server when Docker is not available
            print(f"Simulating stop of server: {server_name} (ID: {server['id']}) (Docker not available)", file=sys.stderr)

        # Update server status
        server['status'] = 'stopped'
        del server['container_id']
        save_servers(servers)

        return jsonify({'status': 'stopped', 'message': f'Server {server_name} stopped successfully'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/servers/<server_name>', methods=['DELETE'])
@require_auth
def delete_server(server_name):
    """Delete a Minecraft server"""
    servers = get_servers()

    # Find the server
    server = next((s for s in servers if s['name'] == server_name), None)
    if not server:
        return jsonify({'error': f'Server {server_name} not found'}), 404

    # Stop the server if it's running
    if server.get('status') == 'running' and 'container_id' in server:
        if docker_available:
            try:
                container = docker_client.containers.get(server['container_id'])
                container.stop()
                container.remove()
            except Exception as e:
                print(f"Error stopping container: {e}", file=sys.stderr)
        else:
            # Simulate stopping the server when Docker is not available
            print(f"Simulating stop of server: {server_name} (ID: {server['id']}) before deletion (Docker not available)", file=sys.stderr)

    # Get the server ID before removing it from the list
    server_id = server['id']

    # Remove server from configuration
    servers = [s for s in servers if s['name'] != server_name]
    save_servers(servers)

    # Remove server directory and its contents
    try:
        server_folder = os.path.join(os.path.dirname(__file__), 'servers', server_id)
        if os.path.exists(server_folder):
            shutil.rmtree(server_folder)
            print(f"Removed server directory: {server_folder}", file=sys.stderr)
    except Exception as e:
        print(f"Error removing server directory: {e}", file=sys.stderr)
        # Continue with the deletion process even if directory removal fails

    return jsonify({'message': f'Server {server_name} deleted successfully'})

@app.route('/api/servers/<server_name>/backup', methods=['POST'])
@require_auth
def toggle_backup(server_name):
    """Toggle backup for a Minecraft server"""
    servers = get_servers()

    # Find the server
    server = next((s for s in servers if s['name'] == server_name), None)
    if not server:
        return jsonify({'error': f'Server {server_name} not found'}), 404

    # Ensure the backup property exists
    if 'backup' not in server:
        server['backup'] = False
        print(f"Added missing backup property to server: {server_name}", file=sys.stderr)

    # Toggle backup status
    server['backup'] = not server['backup']

    # If backup is enabled, ensure the server directory exists
    if server['backup']:
        server_folder = os.path.join(os.path.dirname(__file__), 'servers', server['id'])
        os.makedirs(server_folder, exist_ok=True)
        print(f"Ensured server directory exists for backup: {server_folder}", file=sys.stderr)

    save_servers(servers)

    return jsonify({'message': f'Backup for server {server_name} toggled successfully'})

@app.route('/api/servers/<server_name>/backup', methods=['GET'])
@require_auth
def backup(server_name):
    """Export backup for a Minecraft server and send it as a download"""
    servers = get_servers()

    # Find the server
    server = next((s for s in servers if s['name'] == server_name), None)
    if not server:
        return jsonify({'error': f'Server {server_name} not found'}), 404

    # Ensure the backup property exists
    if 'backup' not in server:
        server['backup'] = False
        save_servers(servers)
        print(f"Added missing backup property to server: {server_name}", file=sys.stderr)

    # Create server directory if it doesn't exist
    server_folder = os.path.join(os.path.dirname(__file__), 'servers', server['id'])
    os.makedirs(server_folder, exist_ok=True)

    # Create a timestamp for the backup file
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    backup_filename = f"server_{server['id']}_backup_{timestamp}"
    backup_path = f"{server_folder}_{backup_filename}"

    # Create the zip archive
    try:
        shutil.make_archive(backup_path, 'zip', server_folder)
        zip_file_path = f"{backup_path}.zip"

        # Send the file as an attachment for download
        return send_file(
            zip_file_path,
            as_attachment=True,
            # Include server name in download filename for user-friendliness
            download_name=f"server_{server['id']}_backup_{timestamp}.zip",
            mimetype='application/zip'
        )
    except Exception as e:
        return jsonify({'error': f'Failed to create backup: {str(e)}'}), 500

@app.route('/api/servers/<server_id>/logs', methods=['GET'])
@require_auth
def get_server_logs(server_id):
    """Get logs for a specific Minecraft server"""
    servers = get_servers()

    # Find the server
    server = next((s for s in servers if s['id'] == server_id), None)
    if not server:
        server = next((s for s in servers if s['name'] == server_id), None)

    if not server:
        return jsonify({'error': f'Server with ID/name {server_id} not found'}), 404

    # Check if server is running
    if server.get('status') != 'running' or 'container_id' not in server:
        return jsonify({'error': 'Server is not running'}), 400

    try:
        if docker_available:
            # Get container logs
            container = docker_client.containers.get(server['container_id'])
            logs = container.logs(tail=100).decode('utf-8')
            return jsonify({'logs': logs})
        else:
            # Simulate logs when Docker is not available
            return jsonify({'logs': f'Simulated logs for server {server["name"]} (Docker not available)'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/servers/<server_id>/command', methods=['POST'])
@require_auth
def send_server_command(server_id):
    """Send a command to a specific Minecraft server"""
    data = request.json
    if not data or 'command' not in data:
        return jsonify({'error': 'Command is required'}), 400

    command = data['command']

    servers = get_servers()

    # Find the server
    server = next((s for s in servers if s['id'] == server_id), None)
    if not server:
        server = next((s for s in servers if s['name'] == server_id), None)

    if not server:
        return jsonify({'error': f'Server with ID/name {server_id} not found'}), 404

    # Check if server is running
    if server.get('status') != 'running' or 'container_id' not in server:
        return jsonify({'error': 'Server is not running'}), 400

    try:
        if docker_available:
            # Execute command in container
            container = docker_client.containers.get(server['container_id'])
            exec_result = container.exec_run(f'rcon-cli {command}')
            response = exec_result.output.decode('utf-8')
            return jsonify({'response': response})
        else:
            # Simulate command execution when Docker is not available
            return jsonify({'response': f'Simulated response for command "{command}" on server {server["name"]} (Docker not available)'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Configure the development server to only watch project files
    # This prevents excessive reloading from site-packages changes

    print("Starting Flask development server with optimized file watching...", file=sys.stderr)

    # Get configuration from environment variables
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', '1').lower() in ('1', 'true', 'yes', 'on')
    reloader_type = os.getenv('FLASK_RELOADER_TYPE', 'stat')

    # Get the current project directory
    project_dir = os.path.dirname(os.path.abspath(__file__))

    # Define files that should trigger a reload (only project files)
    extra_files = []

    # Add all relevant files in the project directory
    for root, dirs, files in os.walk(project_dir):
        # Skip hidden directories and common non-project directories
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', '.git']]

        for file in files:
            if file.endswith(('.py', '.json', '.txt', '.md', '.yml', '.yaml', '.env')):
                extra_files.append(os.path.join(root, file))

    print(f"Monitoring {len(extra_files)} project files for changes", file=sys.stderr)
    print(f"Using {reloader_type} reloader to avoid site-packages monitoring", file=sys.stderr)

    # Run the Flask app with restricted file watching
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            extra_files=extra_files,
            use_reloader=True,
            reloader_type=reloader_type  # Use stat reloader instead of watchdog
        )
    except Exception as e:
        print(f"Error starting Flask server with optimized settings: {e}", file=sys.stderr)
        print("Falling back to basic configuration without reloader...", file=sys.stderr)
        # Fallback to basic configuration if the above fails
        app.run(host=host, port=port, debug=debug, use_reloader=False)
