#!/usr/bin/env python3
"""
Optimized Flask Development Server Starter

This script starts the Flask development server with optimized file watching
to prevent excessive reloading from third-party package changes.

Usage:
    python start_server.py [--no-reloader] [--host HOST] [--port PORT]
"""

import os
import sys
import argparse
from app import app

def main():
    parser = argparse.ArgumentParser(description='Start Flask development server with optimized file watching')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--no-reloader', action='store_true', help='Disable auto-reloader')
    parser.add_argument('--reloader-type', choices=['stat', 'watchdog'], default='stat',
                       help='Type of reloader to use (default: stat)')

    args = parser.parse_args()

    print("=" * 60)
    print("Flask Development Server - Optimized Configuration")
    print("=" * 60)
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    print(f"Reloader: {'Disabled' if args.no_reloader else f'Enabled ({args.reloader_type})'}")
    print("=" * 60)

    if not args.no_reloader:
        # Get the current project directory
        project_dir = os.path.dirname(os.path.abspath(__file__))

        # Define files that should trigger a reload (only project files)
        extra_files = []

        # Add all relevant files in the project directory
        for root, dirs, files in os.walk(project_dir):
            # Skip hidden directories and common non-project directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', '.git']]

            for file in files:
                if file.endswith(('.py', '.json', '.txt', '.md', '.yml', '.yaml', '.env')):
                    extra_files.append(os.path.join(root, file))

        print(f"Monitoring {len(extra_files)} project files for changes")
        print("Site-packages and system directories are excluded from monitoring")
        print("=" * 60)

        app.run(
            host=args.host,
            port=args.port,
            debug=True,
            extra_files=extra_files,
            use_reloader=True,
            reloader_type=args.reloader_type
        )
    else:
        print("Auto-reloader is disabled")
        print("=" * 60)
        app.run(
            host=args.host,
            port=args.port,
            debug=True,
            use_reloader=False
        )

if __name__ == '__main__':
    main()
