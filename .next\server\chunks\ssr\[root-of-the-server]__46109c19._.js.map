{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useForm } from 'react-hook-form';\nimport { login } from '../../lib/auth';\nimport { useAuth } from '../../contexts/AuthContext';\n\ninterface LoginFormData {\n  username: string;\n  password: string;\n}\n\nexport default function LoginPage() {\n  const [error, setError] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n  const { login: authLogin } = useAuth();\n\n  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormData>();\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const user = login(data.username, data.password);\n\n      if (user) {\n        authLogin(user);\n        router.push('/dashboard');\n      } else {\n        setError('Invalid username or password');\n      }\n    } catch (err) {\n      setError('An error occurred during login');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center px-4 relative\">\n      <div className=\"max-w-md w-full space-y-8 fade-in-up\">\n        <div className=\"text-center\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"w-20 h-20 rounded-full flex items-center justify-center relative overflow-hidden card glow\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20\"></div>\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-10 w-10 text-blue-400 relative z-10\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm3 1h6v6H6V5z\" clipRule=\"evenodd\" />\n                <path d=\"M6 5H5v1h1V5z\" />\n                <path d=\"M5 8h1v1H5V8z\" />\n                <path d=\"M8 5h1v1H8V5z\" />\n                <path d=\"M8 8h1v1H8V8z\" />\n                <path d=\"M11 5h1v1h-1V5z\" />\n                <path d=\"M11 8h1v1h-1V8z\" />\n                <path d=\"M5 11h1v1H5v-1z\" />\n                <path d=\"M8 11h1v1H8v-1z\" />\n                <path d=\"M11 11h1v1h-1v-1z\" />\n              </svg>\n            </div>\n          </div>\n          <h2 className=\"text-4xl font-bold gradient-text mb-2\">Admin Login</h2>\n          <p className=\"text-lg text-gray-300 font-light\">\n            Sign in to access the Minecraft Server Admin Panel\n          </p>\n        </div>\n\n        <div className=\"card p-8 fade-in-up delay-200\">\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            {error && (\n              <div className=\"bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg backdrop-blur-sm\">\n                <div className=\"flex items-center\">\n                  <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {error}\n                </div>\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-semibold text-gray-200 mb-3\">\n                Username\n              </label>\n              <input\n                id=\"username\"\n                type=\"text\"\n                {...register('username', { required: 'Username is required' })}\n                className=\"input-field w-full\"\n                placeholder=\"Enter your username\"\n              />\n              {errors.username && (\n                <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {errors.username.message}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-200 mb-3\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                {...register('password', { required: 'Password is required' })}\n                className=\"input-field w-full\"\n                placeholder=\"Enter your password\"\n              />\n              {errors.password && (\n                <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {errors.password.message}\n                </p>\n              )}\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"btn-primary w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Signing in...\n                </div>\n              ) : (\n                <span className=\"flex items-center justify-center\">\n                  <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n                  </svg>\n                  Sign In\n                </span>\n              )}\n            </button>\n          </form>\n\n          <div className=\"mt-8 text-center\">\n            <div className=\"bg-blue-500/10 border border-blue-500/30 rounded-lg p-4\">\n              <p className=\"text-sm text-blue-300 font-medium\">\n                Default credentials: <span className=\"font-mono text-blue-200\">admin</span> / <span className=\"font-mono text-blue-200\">admin123</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEnC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEhE,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,OAAO,CAAA,GAAA,kHAAA,CAAA,QAAK,AAAD,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ;YAE/C,IAAI,MAAM;gBACR,UAAU;gBACV,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAwC,SAAQ;wCAAY,MAAK;;0DACjH,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAiF,UAAS;;;;;;0DACrH,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAKlD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;gCAC/C,uBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAe,SAAQ;0DACxD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoH,UAAS;;;;;;;;;;;4CAEzJ;;;;;;;;;;;;8CAKP,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiD;;;;;;sDAGrF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,YAAY;gDAAE,UAAU;4CAAuB,EAAE;4CAC9D,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAe,SAAQ;8DACxD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAoH,UAAS;;;;;;;;;;;gDAEzJ,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;8CAK9B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiD;;;;;;sDAGrF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,YAAY;gDAAE,UAAU;4CAAuB,EAAE;4CAC9D,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAe,SAAQ;8DACxD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAoH,UAAS;;;;;;;;;;;gDAEzJ,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;8CAK9B,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;;;;;6DAIR,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;sCAOd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAoC;sDAC1B,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;wCAAY;sDAAG,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxI", "debugId": null}}]}