// API client with Firebase authentication
import { getIdToken } from './firebase';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  code?: string;
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = await getIdToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json();
      
      if (!response.ok) {
        return {
          error: data.error || `HTTP ${response.status}: ${response.statusText}`,
          code: data.code || 'HTTP_ERROR'
        };
      }

      return { data };
    } catch (error) {
      return {
        error: 'Failed to parse response',
        code: 'PARSE_ERROR'
      };
    }
  }

  async get<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        headers,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'PUT',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'DELETE',
        headers,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  // File upload with authentication
  async uploadFile<T = any>(endpoint: string, file: File, additionalData?: Record<string, string>): Promise<ApiResponse<T>> {
    try {
      const token = await getIdToken();
      const formData = new FormData();
      formData.append('file', file);

      // Add additional form data if provided
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }

      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }
}

// Create and export a default API client instance
export const apiClient = new ApiClient();

// Export the ApiClient class for custom instances
export { ApiClient };

// Export types
export type { ApiResponse };

// Convenience functions for common API operations
export const api = {
  // Server management
  getServers: () => apiClient.get('/servers'),
  createServer: (serverData: any) => apiClient.post('/servers', serverData),
  updateServer: (serverId: string, serverData: any) => apiClient.put(`/servers/${serverId}`, serverData),
  deleteServer: (serverId: string) => apiClient.delete(`/servers/${serverId}`),
  
  // Server actions
  startServer: (serverId: string) => apiClient.post(`/servers/${serverId}/start`),
  stopServer: (serverId: string) => apiClient.post(`/servers/${serverId}/stop`),
  restartServer: (serverId: string) => apiClient.post(`/servers/${serverId}/restart`),
  
  // File management
  uploadFile: (file: File, additionalData?: Record<string, string>) => 
    apiClient.uploadFile('/files/upload', file, additionalData),
  
  // User profile
  getProfile: () => apiClient.get('/profile'),
  updateProfile: (profileData: any) => apiClient.put('/profile', profileData),
  
  // Health check
  healthCheck: () => apiClient.get('/health'),
};
