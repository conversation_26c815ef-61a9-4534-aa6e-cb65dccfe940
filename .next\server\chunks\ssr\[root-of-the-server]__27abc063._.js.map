{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/lib/auth.ts"], "sourcesContent": ["'use client';\n\n// Simple authentication utilities for admin panel\nexport interface AuthUser {\n  username: string;\n  isAdmin: boolean;\n}\n\n// For demo purposes, using a simple hardcoded admin credential\n// In production, this should be replaced with proper authentication\nconst ADMIN_CREDENTIALS = {\n  username: 'admin',\n  password: 'admin123'\n};\n\nexport const login = (username: string, password: string): AuthUser | null => {\n  if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {\n    const user: AuthUser = {\n      username: ADMIN_CREDENTIALS.username,\n      isAdmin: true\n    };\n    \n    // Store in localStorage for persistence\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_user', JSON.stringify(user));\n    }\n    \n    return user;\n  }\n  \n  return null;\n};\n\nexport const logout = (): void => {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('auth_user');\n  }\n};\n\nexport const getCurrentUser = (): AuthUser | null => {\n  if (typeof window !== 'undefined') {\n    const userStr = localStorage.getItem('auth_user');\n    if (userStr) {\n      try {\n        return JSON.parse(userStr) as AuthUser;\n      } catch {\n        return null;\n      }\n    }\n  }\n  \n  return null;\n};\n\nexport const isAuthenticated = (): boolean => {\n  return getCurrentUser() !== null;\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AAQA,+DAA+D;AAC/D,oEAAoE;AACpE,MAAM,oBAAoB;IACxB,UAAU;IACV,UAAU;AACZ;AAEO,MAAM,QAAQ,CAAC,UAAkB;IACtC,IAAI,aAAa,kBAAkB,QAAQ,IAAI,aAAa,kBAAkB,QAAQ,EAAE;QACtF,MAAM,OAAiB;YACrB,UAAU,kBAAkB,QAAQ;YACpC,SAAS;QACX;QAEA,wCAAwC;QACxC,uCAAmC;;QAEnC;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,uCAAmC;;IAEnC;AACF;AAEO,MAAM,iBAAiB;IAC5B,uCAAmC;;IASnC;IAEA,OAAO;AACT;AAEO,MAAM,kBAAkB;IAC7B,OAAO,qBAAqB;AAC9B", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { AuthUser, getCurrentUser, logout as authLogout } from '../lib/auth';\n\ninterface AuthContextType {\n  user: AuthUser | null;\n  login: (user: AuthUser) => void;\n  logout: () => void;\n  isLoading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<AuthUser | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for existing authentication on mount\n    const currentUser = getCurrentUser();\n    setUser(currentUser);\n    setIsLoading(false);\n  }, []);\n\n  const login = (user: AuthUser) => {\n    setUser(user);\n  };\n\n  const logout = () => {\n    authLogout();\n    setUser(null);\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    isLoading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6CAA6C;QAC7C,MAAM,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;QACjC,QAAQ;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC;QACb,QAAQ;IACV;IAEA,MAAM,SAAS;QACb,CAAA,GAAA,kHAAA,CAAA,SAAU,AAAD;QACT,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/ParticleBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface Particle {\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  size: number;\n  opacity: number;\n}\n\nexport default function ParticleBackground() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particlesRef = useRef<Particle[]>([]);\n  const animationRef = useRef<number>();\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    const createParticles = () => {\n      const particles: Particle[] = [];\n      const particleCount = Math.min(50, Math.floor((canvas.width * canvas.height) / 15000));\n\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          vx: (Math.random() - 0.5) * 0.5,\n          vy: (Math.random() - 0.5) * 0.5,\n          size: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2,\n        });\n      }\n\n      particlesRef.current = particles;\n    };\n\n    const drawParticle = (particle: Particle) => {\n      ctx.save();\n      ctx.globalAlpha = particle.opacity;\n      \n      // Create gradient for particle\n      const gradient = ctx.createRadialGradient(\n        particle.x, particle.y, 0,\n        particle.x, particle.y, particle.size * 2\n      );\n      gradient.addColorStop(0, '#3b82f6');\n      gradient.addColorStop(0.5, '#8b5cf6');\n      gradient.addColorStop(1, 'transparent');\n      \n      ctx.fillStyle = gradient;\n      ctx.beginPath();\n      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n      ctx.fill();\n      \n      ctx.restore();\n    };\n\n    const drawConnections = () => {\n      const particles = particlesRef.current;\n      const maxDistance = 120;\n\n      for (let i = 0; i < particles.length; i++) {\n        for (let j = i + 1; j < particles.length; j++) {\n          const dx = particles[i].x - particles[j].x;\n          const dy = particles[i].y - particles[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < maxDistance) {\n            const opacity = (1 - distance / maxDistance) * 0.2;\n            \n            ctx.save();\n            ctx.globalAlpha = opacity;\n            ctx.strokeStyle = '#3b82f6';\n            ctx.lineWidth = 0.5;\n            ctx.beginPath();\n            ctx.moveTo(particles[i].x, particles[i].y);\n            ctx.lineTo(particles[j].x, particles[j].y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        }\n      }\n    };\n\n    const updateParticles = () => {\n      const particles = particlesRef.current;\n\n      particles.forEach(particle => {\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        // Bounce off edges\n        if (particle.x < 0 || particle.x > canvas.width) {\n          particle.vx *= -1;\n        }\n        if (particle.y < 0 || particle.y > canvas.height) {\n          particle.vy *= -1;\n        }\n\n        // Keep particles within bounds\n        particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n        particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n\n        // Subtle opacity animation\n        particle.opacity += (Math.random() - 0.5) * 0.01;\n        particle.opacity = Math.max(0.1, Math.min(0.7, particle.opacity));\n      });\n    };\n\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      updateParticles();\n      drawConnections();\n      \n      particlesRef.current.forEach(drawParticle);\n      \n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    // Initialize\n    resizeCanvas();\n    createParticles();\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      resizeCanvas();\n      createParticles();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"particle-background\"\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: -1,\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAae,SAAS;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QACpC;QAEA,MAAM,kBAAkB;YACtB,MAAM,YAAwB,EAAE;YAChC,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,MAAM,GAAI;YAE/E,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,UAAU,IAAI,CAAC;oBACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;oBAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;oBAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;oBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;gBACjC;YACF;YAEA,aAAa,OAAO,GAAG;QACzB;QAEA,MAAM,eAAe,CAAC;YACpB,IAAI,IAAI;YACR,IAAI,WAAW,GAAG,SAAS,OAAO;YAElC,+BAA+B;YAC/B,MAAM,WAAW,IAAI,oBAAoB,CACvC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,GACxB,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,GAAG;YAE1C,SAAS,YAAY,CAAC,GAAG;YACzB,SAAS,YAAY,CAAC,KAAK;YAC3B,SAAS,YAAY,CAAC,GAAG;YAEzB,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;YAC5D,IAAI,IAAI;YAER,IAAI,OAAO;QACb;QAEA,MAAM,kBAAkB;YACtB,MAAM,YAAY,aAAa,OAAO;YACtC,MAAM,cAAc;YAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;oBAC7C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC1C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC1C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;oBAE1C,IAAI,WAAW,aAAa;wBAC1B,MAAM,UAAU,CAAC,IAAI,WAAW,WAAW,IAAI;wBAE/C,IAAI,IAAI;wBACR,IAAI,WAAW,GAAG;wBAClB,IAAI,WAAW,GAAG;wBAClB,IAAI,SAAS,GAAG;wBAChB,IAAI,SAAS;wBACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;wBACzC,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;wBACzC,IAAI,MAAM;wBACV,IAAI,OAAO;oBACb;gBACF;YACF;QACF;QAEA,MAAM,kBAAkB;YACtB,MAAM,YAAY,aAAa,OAAO;YAEtC,UAAU,OAAO,CAAC,CAAA;gBAChB,SAAS,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,IAAI,SAAS,EAAE;gBAEzB,mBAAmB;gBACnB,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE;oBAC/C,SAAS,EAAE,IAAI,CAAC;gBAClB;gBACA,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE;oBAChD,SAAS,EAAE,IAAI,CAAC;gBAClB;gBAEA,+BAA+B;gBAC/B,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC;gBAC1D,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,SAAS,CAAC;gBAE3D,2BAA2B;gBAC3B,SAAS,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5C,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,SAAS,OAAO;YACjE;QACF;QAEA,MAAM,UAAU;YACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C;YACA;YAEA,aAAa,OAAO,CAAC,OAAO,CAAC;YAE7B,aAAa,OAAO,GAAG,sBAAsB;QAC/C;QAEA,aAAa;QACb;QACA;QACA;QAEA,gBAAgB;QAChB,MAAM,eAAe;YACnB;YACA;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,QAAQ,CAAC;QACX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}